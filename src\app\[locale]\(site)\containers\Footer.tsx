"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { GoogleMapsEmbed } from "@next/third-parties/google";
import { Link } from "@/i18n/navigation";
import { linksObj } from "../links";
import { socialsItems } from "./SocialButtons";
import Container from "./Container";
import { useSlideAnimation } from "../animations";
import { useTranslations } from "next-intl";
import { ArrowUp } from "lucide-react";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import MapPlaceholder from "@/assets/imgs/map.png";

type props = {
  mapChildren?: React.ReactNode;
};

const Footer = ({ mapChildren }: props) => {
  const t = useTranslations("Footer");
  const [showBackToTop, setShowBackToTop] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 200);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const { scope: titleRef } = useSlideAnimation<HTMLHeadingElement>({
    selector: "span",
  });

  const { scope: contactScope } = useSlideAnimation({
    selector: "div",
    delay: 1,
  });

  const { scope: mapScope } = useSlideAnimation({
    selector: "iframe",
    delay: 1.5,
  });

  const { scope: copyScope } = useSlideAnimation<HTMLParagraphElement>({
    delay: 2,
    direction: "bottom",
    inViewProps: { margin: "10% 0px" },
  });

  return (
    <>
      <Container>
        <footer className="text-secondary-foreground flex flex-col gap-10 px-5 py-10 sm:p-10">
          <h3 ref={titleRef} className="text-3xl font-bold lg:text-6xl">
            <span className="inline-block opacity-0">{t("title")}</span>
          </h3>
          <section className="grid grid-cols-1 gap-10 md:grid-cols-2">
            <article
              ref={mapScope}
              className="relative h-fit w-full overflow-hidden"
            >
              <div className="bg-muted absolute inset-0 flex items-center justify-center text-lg">
                {t("loading")}
                <Image
                  src={MapPlaceholder}
                  alt="Map"
                  width={500}
                  loading="eager"
                  priority={true}
                  height={500}
                  className="absolute inset-0 h-full w-full object-cover"
                />
              </div>
              {mapChildren}
            </article>
            <article
              ref={contactScope}
              id={linksObj.contact.href.replace("#", "")}
              className="flex flex-col gap-4 overflow-hidden text-base font-bold sm:gap-8 sm:text-xl"
            >
              <div className="opacity-0">
                <h2 className="">{t("address.title")}</h2>
                <p>{t("address.street")}</p>
                <p>{t("address.city")}</p>
              </div>
              <div className="opacity-0">
                <p>
                  <a
                    href="tel:+244926131846"
                    className="hover:text-primary transition-colors duration-200"
                  >
                    {t("contact.phone")}
                  </a>
                </p>
                <p>
                  <a
                    href="mailto:<EMAIL>"
                    className="hover:text-primary transition-colors duration-200"
                  >
                    {t("contact.email")}
                  </a>
                </p>
              </div>
              <div className="opacity-0">
                <h5 className="mb-2">{t("newsletter.title")}</h5>
                <form className="flex flex-col items-start gap-4">
                  <Input
                    type="email"
                    placeholder={t("newsletter.emailPlaceholder")}
                    className="bg-primary-foreground focus:border-background max-w-sm rounded-none border-transparent [box-shadow:inset_0_0_10px_5px_rgba(0,0,0,0.5)]"
                  />
                  <span className="flex w-full flex-wrap items-center justify-between gap-4">
                    <Button
                      variant="outline"
                      className="border-background text-background"
                    >
                      {t("newsletter.submit")}
                    </Button>
                    <span className="hidden items-center gap-4 sm:flex">
                      {socialsItems.map(({ href, Icon }, index) => {
                        return (
                          <Link key={index} href={href}>
                            <Icon
                              className="size-7"
                              strokeWidth={1.25}
                              innerPathClassName="fill-primary-foreground"
                            />
                          </Link>
                        );
                      })}
                    </span>
                  </span>
                </form>
              </div>
            </article>
          </section>
          <hr className="border border-current sm:border" />
          <p
            ref={copyScope}
            className="-mt-4 text-xs font-medium opacity-0 sm:text-base"
          >
            {t("copyright")}
          </p>
        </footer>
      </Container>
      <Button
        variant="outline"
        size="icon"
        title={t("backToTop")}
        className={cn(
          "border-muted fixed right-4 bottom-4 z-50 rounded-full transition-all duration-300",
          showBackToTop
            ? "translate-y-0 opacity-100"
            : "translate-y-16 opacity-0",
        )}
        onClick={scrollToTop}
        aria-label={t("backToTop")}
      >
        <ArrowUp className="size-5" />
      </Button>
    </>
  );
};

export default Footer;
