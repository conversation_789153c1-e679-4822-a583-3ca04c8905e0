import "../../globals.css";
import { cn } from "@/lib/utils";
import { mainFont } from "../../fonts";
import Header from "./containers/Header";
import { ReactLenis } from "@/lib/lenis";
import Footer from "./containers/Footer";

import { hasLocale, NextIntlClientProvider } from "next-intl";
import {
  getMessages,
  getTranslations,
  setRequestLocale,
} from "next-intl/server";
import { Locale, routing } from "@/i18n/routing";
import { GoogleMapsEmbed } from "@next/third-parties/google";
import { notFound } from "next/navigation";

export async function generateMetadata(props: {
  params: Promise<{ locale: string }>;
}) {
  const params = await props.params;

  const { locale } = params;

  const t = await getTranslations({ locale, namespace: "Metadata" });

  return {
    title: t("title"),
    description: t("description"),
  };
}

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: { locale: Locale };
}>) {
  const { locale } = await params;

  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  setRequestLocale(locale);

  // const messages = await getMessages();

  return (
    <ReactLenis root>
      <html lang={locale} className="min-h-screen">
        <body
          className={cn(
            "from-primary to-primary-foreground flex min-h-screen flex-col bg-linear-to-b font-sans antialiased",
            mainFont.variable,
            // titleFont.variable,
          )}
        >
          <NextIntlClientProvider>
            <Header />
            {children}
            <Footer
              mapChildren={
                <GoogleMapsEmbed
                  apiKey={process.env.GOOGLE_MAPS_API_KEY || ""}
                  height={350}
                  width="100%"
                  mode="place"
                  q="259R+VG Luanda"
                  zoom="20"
                  language="pt"
                  maptype="satellite"
                  style="position: relative; width: 100%; height: 100%; opacity: 0; z-index: 10;"
                />
              }
            />
          </NextIntlClientProvider>
        </body>
      </html>
    </ReactLenis>
  );
}
